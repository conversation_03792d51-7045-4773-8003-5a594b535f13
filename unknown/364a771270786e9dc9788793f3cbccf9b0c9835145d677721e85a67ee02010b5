<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MURAi Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>MURAi Extension Test Page</h1>
    
    <div class="test-section">
        <h2>Test Section 1: Basic Terms</h2>
        <p>This paragraph contains the word gago in the middle of a sentence.</p>
        <p>Here is another sentence with haop mentioned casually.</p>
        <p>And this one has putangina at the end of the sentence.</p>
    </div>
    
    <div class="test-section">
        <h2>Test Section 2: Multiple Occurrences</h2>
        <p>The word gago appears multiple times: gago, gago, and gago again.</p>
        <p>We also have haop appearing here and haop appearing there.</p>
    </div>
    
    <div class="test-section">
        <h2>Test Section 3: Mixed Content</h2>
        <p>This is a normal paragraph with some <strong>gago</strong> in bold text.</p>
        <p>Here's a <em>haop</em> in italic text.</p>
        <p>And <span style="color: blue;">putangina</span> in colored text.</p>
    </div>
    
    <div class="test-section">
        <h2>Test Section 4: Case Variations</h2>
        <p>GAGO in uppercase</p>
        <p>HaOp in mixed case</p>
        <p>PUTANGINA in all caps</p>
    </div>
    
    <div class="test-section">
        <h2>Test Section 5: Non-Matching Text</h2>
        <p>This paragraph contains no flagged words.</p>
        <p>Neither does this one.</p>
        <p>Or this one either.</p>
    </div>
    
    <script>
        console.log("Test page loaded");
        console.log("Page contains the following test terms:");
        console.log("- gago");
        console.log("- haop"); 
        console.log("- putangina");
    </script>
</body>
</html> 