import React from 'react';

const SyncSection = ({ 
  lastSyncTime, 
  isSyncing, 
  syncStatus, 
  onSyncClick,
  userEmail 
}) => {
  const formatSyncTime = (time) => {
    if (!time) return 'Never';
    const now = new Date();
    const diff = now - time;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getSyncStatusColor = () => {
    switch (syncStatus) {
      case 'Synced': return '#10b981';
      case 'Syncing...': return '#3b82f6';
      case 'Sync failed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getSyncIcon = () => {
    if (isSyncing) return '🔄';
    switch (syncStatus) {
      case 'Synced': return '✅';
      case 'Sync failed': return '❌';
      default: return '📱';
    }
  };

  return (
    <div style={{
      background: '#f8fafc',
      border: '1px solid #e2e8f0',
      borderRadius: '12px',
      padding: '16px',
      margin: '16px 0'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '12px'
      }}>
        <h3 style={{
          margin: 0,
          fontSize: '14px',
          fontWeight: '600',
          color: '#374151',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          {getSyncIcon()} Mobile App Sync
        </h3>
        <button
          onClick={onSyncClick}
          disabled={isSyncing}
          style={{
            background: isSyncing ? '#9ca3af' : '#2d72d9',
            color: '#fff',
            border: 'none',
            borderRadius: '6px',
            padding: '6px 12px',
            fontSize: '12px',
            fontWeight: '600',
            cursor: isSyncing ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
        >
          {isSyncing ? '⏳' : '🔄'} {isSyncing ? 'Syncing...' : 'Sync Now'}
        </button>
      </div>
      
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '12px'
      }}>
        <div>
          <div style={{ color: '#6b7280', marginBottom: '2px' }}>
            Account: {userEmail || 'Not available'}
          </div>
          <div style={{ color: '#6b7280' }}>
            Last sync: {formatSyncTime(lastSyncTime)}
          </div>
        </div>
        <div style={{
          color: getSyncStatusColor(),
          fontWeight: '600',
          fontSize: '11px'
        }}>
          {syncStatus}
        </div>
      </div>
      
      {syncStatus === 'Not synced' && (
        <div style={{
          marginTop: '8px',
          padding: '8px',
          background: '#fef3c7',
          border: '1px solid #fbbf24',
          borderRadius: '6px',
          fontSize: '11px',
          color: '#92400e'
        }}>
          💡 Click "Sync Now" to get your latest settings from the mobile app
        </div>
      )}
    </div>
  );
};

export default SyncSection;
