<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MURAi Detection & Reporting Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #02B97F;
        }
        .instructions {
            background: #dbeafe;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
        }
        button {
            background: #02B97F;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px 4px;
            font-size: 14px;
            transition: background 0.2s;
        }
        button:hover {
            background: #029f6b;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        #log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #10b981; }
        .log-error { color: #ef4444; }
        .log-info { color: #3b82f6; }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success { background: #dcfce7; color: #166534; }
        .status-error { background: #fecaca; color: #991b1b; }
        .status-warning { background: #fef3c7; color: #92400e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ MURAi Detection & Reporting Test</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li><strong>Login to Extension:</strong> Make sure you're logged into the MURAi extension</li>
                <li><strong>Open Console:</strong> Press F12 to open browser developer tools</li>
                <li><strong>Check Auth:</strong> Click "Check Auth Status" to verify login</li>
                <li><strong>Add Test Content:</strong> Click "Add Inappropriate Content" to trigger detection</li>
                <li><strong>Monitor Logs:</strong> Watch both browser console and the log area below</li>
                <li><strong>Test Reporting:</strong> Try reporting highlighted words manually</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 Test Controls</h3>
            <button onclick="checkAuthStatus()">Check Auth Status</button>
            <button onclick="addInappropriateContent()">Add Inappropriate Content</button>
            <button onclick="addCleanContent()">Add Clean Content</button>
            <button onclick="clearContent()">Clear Content</button>
            <button onclick="triggerManualDetection()">Manual Detection</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="test-section">
            <h3>📊 Status</h3>
            <p><strong>Extension Status:</strong> <span id="extension-status" class="status status-warning">Checking...</span></p>
            <p><strong>Auth Status:</strong> <span id="auth-status" class="status status-warning">Checking...</span></p>
            <p><strong>Detection Status:</strong> <span id="detection-status" class="status status-warning">Waiting...</span></p>
        </div>

        <div id="content-area">
            <h3>📝 Test Content Area</h3>
            <p>This area will be populated with test content to trigger the MURAi detection system.</p>
            <p>The extension should automatically scan this content for inappropriate words.</p>
        </div>

        <div class="test-section">
            <h3>📋 Console Logs</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        // Enhanced logging system
        const logDiv = document.getElementById('log');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Override console methods
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warning');
        };

        // Test functions
        function checkAuthStatus() {
            console.log('🔍 Checking authentication status...');
            
            const authData = localStorage.getItem('murai_auth_data');
            const loggedIn = localStorage.getItem('murai_logged_in');
            
            console.log('- Logged in flag:', loggedIn);
            console.log('- Auth data exists:', !!authData);
            
            if (authData) {
                try {
                    const parsed = JSON.parse(authData);
                    console.log('- Token exists:', !!parsed.token);
                    console.log('- User email:', parsed.email || 'Not found');
                    console.log('- Login time:', parsed.loginTime || 'Not found');
                    
                    document.getElementById('auth-status').textContent = 'Authenticated';
                    document.getElementById('auth-status').className = 'status status-success';
                } catch (e) {
                    console.error('- Error parsing auth data:', e);
                    document.getElementById('auth-status').textContent = 'Auth Error';
                    document.getElementById('auth-status').className = 'status status-error';
                }
            } else {
                console.warn('- No authentication data found');
                document.getElementById('auth-status').textContent = 'Not Authenticated';
                document.getElementById('auth-status').className = 'status status-error';
            }
            
            // Check if content script is loaded
            if (window.muraiContentScript) {
                console.log('✅ Content script is loaded and accessible');
                document.getElementById('extension-status').textContent = 'Active';
                document.getElementById('extension-status').className = 'status status-success';
            } else {
                console.warn('❌ Content script not found on window object');
                document.getElementById('extension-status').textContent = 'Not Active';
                document.getElementById('extension-status').className = 'status status-error';
            }
        }

        function addInappropriateContent() {
            console.log('➕ Adding test content with inappropriate words...');

            const contentArea = document.getElementById('content-area');
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <h3>🚨 Test Content with Inappropriate Words</h3>
                <p>This is a test with some inappropriate words: <strong>stupid</strong>, <strong>gago</strong>, <strong>gagi</strong>, <strong>putangina</strong>, <strong>fuck</strong>.</p>
                <p>Another sentence with bad words: <strong>shit</strong>, <strong>bitch</strong>, <strong>idiot</strong>, <strong>tanga</strong>.</p>
                <p>Mixed content: This is normal text with some bad words like <strong>damn</strong> and <strong>hell</strong>.</p>
                <p>More test words: <strong>bobo</strong>, <strong>asshole</strong>, <strong>motherfucker</strong>.</p>
                <p>Filipino test: <strong>gagi ka</strong> <strong>dapat kinuhamo</strong> yong test.</p>
                <p>Additional content: This content should trigger the MURAi detection system automatically.</p>
            `;
            contentArea.appendChild(testDiv);
            
            console.log('✅ Test content added successfully');
            document.getElementById('detection-status').textContent = 'Content Added';
            document.getElementById('detection-status').className = 'status status-warning';
            
            // Wait a moment then check for detection
            setTimeout(() => {
                console.log('🔍 Checking if detection occurred...');
                const highlights = document.querySelectorAll('.murai-highlight, .murai-masked-word');
                console.log(`- Found ${highlights.length} highlighted/masked elements`);
                
                if (highlights.length > 0) {
                    console.log('✅ Detection successful - words were highlighted/masked');
                    document.getElementById('detection-status').textContent = 'Detected';
                    document.getElementById('detection-status').className = 'status status-success';
                } else {
                    console.warn('⚠️ No highlights found - detection may not be working');
                    document.getElementById('detection-status').textContent = 'No Detection';
                    document.getElementById('detection-status').className = 'status status-error';
                }
            }, 2000);
        }

        function addCleanContent() {
            console.log('➕ Adding clean test content...');
            
            const contentArea = document.getElementById('content-area');
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <h3>✅ Clean Test Content</h3>
                <p>This is completely clean content with no inappropriate words.</p>
                <p>Just normal, family-friendly text that should not trigger any detection.</p>
                <p>This content is safe and appropriate for all audiences.</p>
            `;
            contentArea.appendChild(testDiv);
            console.log('✅ Clean content added successfully');
        }

        function clearContent() {
            console.log('🧹 Clearing test content...');
            
            const contentArea = document.getElementById('content-area');
            contentArea.innerHTML = `
                <h3>📝 Test Content Area</h3>
                <p>This area will be populated with test content to trigger the MURAi detection system.</p>
                <p>The extension should automatically scan this content for inappropriate words.</p>
            `;
            
            document.getElementById('detection-status').textContent = 'Waiting...';
            document.getElementById('detection-status').className = 'status status-warning';
            console.log('✅ Content cleared successfully');
        }

        function triggerManualDetection() {
            console.log('🔄 Triggering manual detection...');
            
            if (window.muraiContentScript) {
                console.log('📡 Content script found, calling detectAndHighlight()');
                try {
                    window.muraiContentScript.detectAndHighlight();
                    console.log('✅ Manual detection triggered successfully');
                } catch (error) {
                    console.error('❌ Error triggering manual detection:', error);
                }
            } else {
                console.error('❌ Content script not available for manual detection');
            }
        }

        function clearLogs() {
            logDiv.innerHTML = '';
            console.log('🧹 Logs cleared');
        }

        // Initialize page
        window.addEventListener('load', () => {
            console.log('🚀 MURAi Detection Test Page Loaded');
            console.log('📍 Current URL:', window.location.href);
            console.log('🔧 Open browser console (F12) for detailed logs');
            
            // Auto-check status after a short delay
            setTimeout(checkAuthStatus, 1000);
        });

        // Monitor for MURAi activity
        setInterval(() => {
            const highlights = document.querySelectorAll('.murai-highlight, .murai-masked-word');
            if (highlights.length > 0 && document.getElementById('detection-status').textContent !== 'Detected') {
                console.log(`🎯 Detection activity detected: ${highlights.length} elements highlighted/masked`);
                document.getElementById('detection-status').textContent = 'Detected';
                document.getElementById('detection-status').className = 'status status-success';
            }
        }, 2000);
    </script>
</body>
</html>
