// Simple script to copy the source content script to dist folder
import fs from 'fs';
import path from 'path';

const sourceFile = 'src/content/ContentScript.jsx';
const destFile = 'dist/content.js';

try {
  // Read the source file
  const sourceContent = fs.readFileSync(sourceFile, 'utf8');
  
  // Write to destination
  fs.writeFileSync(destFile, sourceContent);
  
  console.log('✅ Successfully copied ContentScript.jsx to dist/content.js');
  console.log('📁 Source:', sourceFile);
  console.log('📁 Destination:', destFile);
  console.log('📊 File size:', sourceContent.length, 'characters');
  
} catch (error) {
  console.error('❌ Error copying file:', error.message);
}
