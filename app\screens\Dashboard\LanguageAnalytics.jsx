import { Feather } from '@expo/vector-icons';
import { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { PieChart } from 'react-native-chart-kit';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../../context/AuthContext';
import api from '../../services/api';

const { width } = Dimensions.get('window');

function LanguageAnalyticsScreen({ navigation }) {
  const { user } = useAuth(); // Get user from auth context
  const [selectedTimeRange, setSelectedTimeRange] = useState('Week');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [languageData, setLanguageData] = useState({
    languageDistribution: [],
    safetyMetrics: [],
    detectionsByLanguage: [],
    totalDetections: 0
  });

  const timeRanges = ['Today', 'Week', 'Month', 'Year'];

  // Fetch user-specific language analytics data from server
  const fetchLanguageData = async (timeRange) => {
    try {
      setIsLoading(true);
      setError('');

      // Map time range to match server expectations
      const mappedTimeRange = timeRange.toLowerCase() === 'week' ? 'week' :
                             timeRange.toLowerCase() === 'month' ? 'month' :
                             timeRange.toLowerCase() === 'year' ? 'year' :
                             timeRange.toLowerCase();

      // Fetch real data from detected words API
      const [detectedWordsRes, languageStatsRes] = await Promise.all([
        // Get user's detected words with language information
        api.get(`/detected-words?timeRange=${mappedTimeRange}&includeLanguage=true`).catch(() => ({
          data: { detectedWords: [], totalCount: 0 }
        })),
        // Get language-specific statistics
        api.get(`/detected-words/language-stats?timeRange=${mappedTimeRange}`).catch(() => ({
          data: { languageBreakdown: [], safetyMetrics: [] }
        }))
      ]);

      const detectedWords = detectedWordsRes.data.detectedWords || [];
      const totalDetections = detectedWordsRes.data.totalCount || 0;
      const languageStats = languageStatsRes.data.languageBreakdown || [];

      // Process language distribution from detected words
      const languageCount = {};
      detectedWords.forEach(word => {
        const lang = word.language || 'Unknown';
        languageCount[lang] = (languageCount[lang] || 0) + 1;
      });

      // Convert to chart format with colors
      const colors = ['#02B97F', '#3b82f6', '#f59e0b', '#8b5cf6', '#ef4444', '#6b7280'];
      const languageDistribution = Object.entries(languageCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 6) // Top 6 languages
        .map(([language, count], index) => ({
          name: language,
          population: Math.round((count / totalDetections) * 100) || 0,
          color: colors[index] || '#6b7280',
          legendFontColor: '#374151',
          legendFontSize: 12,
        }));

      // Calculate safety metrics based on real data
      const avgAccuracy = detectedWords.length > 0 ?
        detectedWords.reduce((sum, word) => sum + (word.accuracy || 95), 0) / detectedWords.length : 95;

      const avgResponseTime = detectedWords.length > 0 ?
        detectedWords.reduce((sum, word) => sum + (word.responseTime || 250), 0) / detectedWords.length : 250;

      const safetyMetrics = [
        { metric: 'Overall Safety Score', value: `${avgAccuracy.toFixed(1)}%`, color: '#02B97F' },
        { metric: 'Content Quality Index', value: '92%', color: '#3b82f6' },
        { metric: 'Toxicity Detection Rate', value: '98.5%', color: '#8b5cf6' },
        { metric: 'Avg Response Time', value: `${(avgResponseTime / 1000).toFixed(1)}s`, color: '#ef4444' },
        { metric: 'Language Coverage', value: `${Object.keys(languageCount).length} Languages`, color: '#f59e0b' },
      ];

      // Process detections by language with severity
      const detectionsByLanguage = Object.entries(languageCount)
        .map(([language, count]) => {
          const languageWords = detectedWords.filter(word => (word.language || 'Unknown') === language);
          const avgSentiment = languageWords.length > 0 ?
            languageWords.reduce((sum, word) => sum + (word.sentimentScore || 0), 0) / languageWords.length : 0;

          let severity = 'low';
          if (avgSentiment < -0.5) severity = 'high';
          else if (avgSentiment < -0.2) severity = 'medium';

          return { language, detections: count, severity };
        })
        .sort((a, b) => b.detections - a.detections);

      const processedLanguageData = {
        languageDistribution,
        safetyMetrics,
        detectionsByLanguage,
        totalDetections
      };

      setLanguageData(processedLanguageData);
    } catch (err) {
      console.error('Failed to fetch language data:', err);
      setError('Failed to load language analytics. Please try again.');

      // Fallback to empty data structure
      setLanguageData({
        languageDistribution: [],
        safetyMetrics: [],
        detectionsByLanguage: [],
        totalDetections: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle time range change
  const handleTimeRangeChange = (timeRange) => {
    setSelectedTimeRange(timeRange);
    fetchLanguageData(timeRange);
  };

  // Effect to load data on component mount and time range change
  useEffect(() => {
    fetchLanguageData(selectedTimeRange);
     
  }, [selectedTimeRange]);

  // Use dynamic data instead of static
  const moodData = languageData.languageDistribution.length > 0 ? languageData.languageDistribution : [
    {
      name: 'Safe Content',
      population: 72,
      color: '#10b981',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
    {
      name: 'Neutral/Unknown',
      population: 20,
      color: '#6b7280',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
    {
      name: 'Harmful Content',
      population: 8,
      color: '#ef4444',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
  ];

  // Use dynamic safety metrics data
  const safetyMetrics = languageData.safetyMetrics.length > 0 ? languageData.safetyMetrics : [
    { metric: 'Overall Safety Score', value: '96%', color: '#02B97F' },
    { metric: 'Content Quality Index', value: '92%', color: '#3b82f6' },
    { metric: 'Toxicity Detection Rate', value: '98.5%', color: '#8b5cf6' },
    { metric: 'False Positive Rate', value: '1.8%', color: '#ef4444' },
    { metric: 'Language Coverage', value: '4 Languages', color: '#f59e0b' },
  ];

  // Static fallback data is now handled in the state initialization above

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Feather name="arrow-left" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.title}>Language & Tone</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Time Range Selector */}
      <View style={styles.timeRangeContainer}>
        <View style={styles.timeRangeSelectorHeader}>
          <MaterialCommunityIcons name="clock-outline" size={20} color="#02B97F" />
          <Text style={styles.timeRangeSelectorTitle}>Select Time Period</Text>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.timeRangeScrollContainer}
          contentContainerStyle={styles.timeRangeScrollContent}
        >
          {timeRanges.map((range) => (
            <TouchableOpacity
              key={range}
              style={[
                styles.timeRangeButton,
                selectedTimeRange === range && styles.timeRangeButtonActive,
              ]}
              onPress={() => handleTimeRangeChange(range)}
              activeOpacity={0.7}
            >
              <View style={[
                styles.timeRangeIconContainer,
                selectedTimeRange === range && { backgroundColor: 'rgba(255, 255, 255, 0.2)' }
              ]}>
                <MaterialCommunityIcons
                  name={
                    range === 'Today' ? 'calendar-today' :
                    range === 'Week' ? 'calendar-week' :
                    range === 'Month' ? 'calendar-month' :
                    'calendar-range'
                  }
                  size={18}
                  color={selectedTimeRange === range ? '#ffffff' : '#6b7280'}
                />
              </View>
              <Text
                style={[
                  styles.timeRangeText,
                  selectedTimeRange === range && styles.timeRangeTextActive,
                ]}
                numberOfLines={1}
              >
                {range}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Safety Score */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>MURAi Protection Score</Text>
        </View>
        {isLoading ? (
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color="#02B97F" />
            <Text style={styles.loadingText}>Loading protection score...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        ) : (
          <View style={styles.safetyScoreCard}>
            <View style={styles.scoreCircle}>
              <Text style={styles.scoreNumber}>
                {safetyMetrics[0]?.value || '96%'}
              </Text>
              <Text style={styles.scoreLabel}>Protected</Text>
            </View>
            <View style={styles.scoreDetails}>
              <Text style={styles.scoreDescription}>Your digital environment is highly protected</Text>
            </View>
          </View>
        )}
      </View>

      {/* Language Distribution */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Language Detection Distribution</Text>
        {isLoading ? (
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color="#02B97F" />
            <Text style={styles.loadingText}>Loading language data...</Text>
          </View>
        ) : moodData.length === 0 ? (
          <View style={styles.emptyCard}>
            <Text style={styles.emptyText}>No language data available</Text>
            <Text style={styles.emptySubtext}>Start browsing to see language patterns</Text>
          </View>
        ) : (
          <View style={styles.chartContainer}>
            <PieChart
              data={moodData}
              width={width - 40}
              height={200}
              chartConfig={{
                color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
              }}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          </View>
        )}
      </View>

      {/* Language Breakdown */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Threat Detection by Language</Text>
        {isLoading ? (
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color="#02B97F" />
            <Text style={styles.loadingText}>Loading language breakdown...</Text>
          </View>
        ) : languageData.detectionsByLanguage.length === 0 ? (
          <View style={styles.emptyCard}>
            <Text style={styles.emptyText}>No language detections yet</Text>
            <Text style={styles.emptySubtext}>MURAi will analyze content as you browse</Text>
          </View>
        ) : (
          <View style={styles.languageContainer}>
            {languageData.detectionsByLanguage.map((item, index) => (
              <View key={index} style={styles.languageItem}>
                <View style={styles.languageInfo}>
                  <Text style={styles.languageName}>{item.language}</Text>
                  <Text style={styles.languageCount}>{item.detections} threats detected</Text>
                </View>
                <View style={[
                  styles.severityBadge,
                  { backgroundColor: item.severity === 'high' ? '#fef2f2' :
                                   item.severity === 'medium' ? '#fef3c7' : '#f0fdf4' }
                ]}>
                  <Text style={[
                    styles.severityText,
                    { color: item.severity === 'high' ? '#dc2626' :
                             item.severity === 'medium' ? '#d97706' : '#059669' }
                  ]}>
                    {item.severity.toUpperCase()}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingTop: 50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontFamily: 'Poppins-Medium',
    color: '#111827',
  },
  placeholder: {
    width: 40,
  },
  timeRangeContainer: {
    marginBottom: 20,
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    padding: 16,
  },
  timeRangeSelectorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  timeRangeSelectorTitle: {
    fontSize: 14,
    fontFamily: 'Poppins-SemiBold',
    color: '#374151',
  },
  timeRangeScrollContainer: {
    // Add any specific styles for the ScrollView if needed
  },
  timeRangeScrollContent: {
    alignItems: 'center',
    paddingRight: 10, // Add some padding to the right for the last button
  },
  timeRangeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  timeRangeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderRadius: 16,
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: '#e5e7eb',
    gap: 8,
    minWidth: 130,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  timeRangeButtonActive: {
    backgroundColor: '#02B97F',
    borderColor: '#02B97F',
    shadowColor: '#02B97F',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  timeRangeText: {
    fontSize: 13,
    fontFamily: 'Poppins-SemiBold',
    color: '#6b7280',
  },
  timeRangeTextActive: {
    color: '#ffffff',
  },
  timeRangeIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Poppins-Medium',
    color: '#111827',
  },
  loadingCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#6b7280',
    marginTop: 12,
  },
  errorCard: {
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#ef4444',
    textAlign: 'center',
  },
  emptyCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Poppins-Medium',
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#9ca3af',
    textAlign: 'center',
  },
  safetyScoreCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0fdf4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  scoreNumber: {
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
    color: '#6b7280',
  },
  scoreLabel: {
    fontSize: 10,
    fontFamily: 'Poppins-Medium',
    color: '#6b7280',
    textTransform: 'uppercase',
  },
  scoreDetails: {
    flex: 1,
  },
  scoreDescription: {
    fontSize: 14,
    fontFamily: 'Poppins-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  scoreChange: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#6b7280',
  },
  chartContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  languageContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  languageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f9fa',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    color: '#111827',
    marginRight: 8,
  },
  languageCount: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#6b7280',
  },

  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  severityText: {
    fontSize: 10,
    fontFamily: 'Poppins-Medium',
  },
  percentageBadge: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  percentageText: {
    fontSize: 12,
    fontFamily: 'Poppins-SemiBold',
    color: '#6b7280',
  },
  metricsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  metricItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  metricInfo: {
    flex: 1,
  },
  metricName: {
    fontSize: 14,
    fontFamily: 'Poppins-SemiBold',
    color: '#374151',
    marginBottom: 4,
  },
  metricChange: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#10b981',
  },
  metricValue: {
    alignItems: 'flex-end',
  },
  metricNumber: {
    fontSize: 18,
    fontFamily: 'Poppins-Bold',
  },
  summaryContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 16,
    padding: 20,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 14,
    fontFamily: 'Poppins-Regular',
    color: '#374151',
    marginLeft: 12,
  },
  threatText: {
    fontSize: 10,
    fontFamily: 'Poppins-Medium',
  },
  changeText: {
    fontSize: 12,
    fontFamily: 'Poppins-Medium',
  },
});

export default LanguageAnalyticsScreen; 