const form = document.getElementById('login-form');
const successMsg = document.getElementById('success-message');
const openPopupBtn = document.getElementById('open-popup-btn');
const API_BASE_URL = 'https://murai-server.onrender.com/api';

form.addEventListener('submit', async function(e) {
  e.preventDefault();

  const email = document.getElementById('username').value;
  const password = document.getElementById('password').value;

  // Show loading state
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = 'Logging in...';
  submitBtn.disabled = true;

  try {
    // Authenticate with mobile app's backend
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Login failed');
    }

    // Store authentication data
    const authData = {
      token: data.token,
      user: data.user,
      loginTime: new Date().toISOString()
    };

    localStorage.setItem('murai_logged_in', 'true');
    localStorage.setItem('murai_auth_data', JSON.stringify(authData));

    if (window.chrome && chrome.storage && chrome.storage.local) {
      chrome.storage.local.set({
        murai_logged_in: 'true',
        murai_auth_data: authData
      });
    }

    // Load user settings from mobile app
    await loadUserSettings(data.token);

    form.style.display = 'none';
    successMsg.style.display = 'block';
    openPopupBtn.style.display = 'block';

  } catch (error) {
    console.error('Login error:', error);
    alert('Login failed: ' + error.message);

    // Reset button state
    submitBtn.textContent = originalText;
    submitBtn.disabled = false;
  }
});

async function loadUserSettings(token) {
  try {
    const response = await fetch(`${API_BASE_URL}/users/preferences`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const preferences = await response.json();

      // Convert mobile app preferences to extension format
      const extensionSettings = {
        language: preferences.language || 'Mixed',
        sensitivity: preferences.sensitivity || 'High',
        protectionEnabled: true,
        whitelistTerms: preferences.whitelistTerms || [],
        whitelistWebsites: preferences.whitelistSite || [],
        flagStyle: preferences.flagStyle || 'asterisk',
        showHighlight: preferences.isHighlighted !== false,
        highlightColor: preferences.color || '#ffeb3b'
      };

      // Save settings to chrome storage
      if (window.chrome && chrome.storage && chrome.storage.sync) {
        await chrome.storage.sync.set(extensionSettings);
        console.log('User settings synced from mobile app');
      }
    }
  } catch (error) {
    console.warn('Failed to load user settings:', error);
  }
}

openPopupBtn.addEventListener('click', function() {
  window.close(); // Suggest user to open the extension popup
});