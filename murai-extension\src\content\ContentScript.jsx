// Language-specific inappropriate terms
const TERMS_BY_LANGUAGE = {
  'Tagalog': [
    'gago', 'gagi', 'gaga', 'gagu', 'gago ka', 'gagi ka', 'gaga ka',
    'haop', 'putangina', 'tangina', 'pakyu', 'puta', 'putang ina',
    'bobo', 'tanga', 'ulol', 'tarantado', 'walanghiya', 'bastos', 'kupal',
    'tangina mo', 'putang ina mo', 'gago ka talaga', 'gagi ka talaga',
    'kinuha', 'kinuhamo', 'dapat', 'walang hiya', 'walang-hiya'
  ],
  'English': [
    'stupid', 'idiot', 'moron', 'dumb', 'fool', 'asshole', 'bastard',
    'bitch', 'fuck', 'shit', 'damn', 'hell', 'crap', 'piss', 'dick',
    'cock', 'pussy', 'whore', 'slut', 'cunt', 'motherfucker', 'fucker',
    'dumbass', 'jackass', 'dickhead', 'prick', 'twat', 'wanker'
  ],
  'Mixed': [
    // Tagalog terms
    'gago', 'haop', 'putangina', 'tangina', 'pakyu', 'puta', 'gago ka',
    'bobo', 'tanga', 'ulol', 'gaga', 'gagi', 'gagu', 'gago', 'gagu',
    'tarantado', 'walanghiya', 'bastos', 'kupal', 'ulol', 'gago',
    'tangina mo', 'putang ina', 'putang ina mo', 'gago ka talaga',
    // English terms
    'stupid', 'idiot', 'moron', 'dumb', 'fool', 'asshole', 'bastard',
    'bitch', 'fuck', 'shit', 'damn', 'hell', 'crap', 'piss', 'dick',
    'cock', 'pussy', 'whore', 'slut', 'cunt', 'motherfucker', 'fucker',
    'dumbass', 'jackass', 'dickhead', 'prick', 'twat', 'wanker'
  ]
};

class ContentScript {
  constructor() {
    this.settings = {
      protectionEnabled: true,
      language: 'Mixed',
      sensitivity: 'High',
      whitelistTerms: [],
      whitelistWebsites: [],
      flagStyle: 'asterisk',
      showHighlight: true,
      highlightColor: '#ffeb3b'
    };
    this.detectedTerms = [];
    this.isInitialized = false;
    this.isProcessing = false;
    this.processedElements = new WeakSet(); // Track processed elements
    this.reportedWords = new Set(); // Track already reported words to avoid duplicates
    this.API_BASE_URL = 'http://localhost:3000/api';
  }

  async init() {
    if (this.isInitialized) return;

    console.log('MURAi: Initializing content script...');
    console.log('MURAi: Current URL:', window.location.href);

    // Load settings from storage
    await this.loadSettings();

    console.log('MURAi: Settings loaded:', this.settings);

    // Start detection
    this.startDetection();

    // Listen for settings updates
    this.setupMessageListener();

    this.isInitialized = true;
    console.log('MURAi: Content script initialized');
  }

  async loadSettings() {
    try {
      // eslint-disable-next-line no-undef
      const result = await chrome.storage.sync.get([
        'protectionEnabled',
        'language',
        'sensitivity',
        'whitelistTerms',
        'whitelistWebsites',
        'flagStyle',
        'showHighlight',
        'highlightColor'
      ]);

      console.log('MURAi: Raw settings from storage:', result);
      
      this.settings = { ...this.settings, ...result };
      console.log('MURAi: Final settings loaded:', this.settings);
      console.log('MURAi: Flag style is:', this.settings.flagStyle);
    } catch (error) {
      console.error('MURAi: Error loading settings:', error);
    }
  }

  setupMessageListener() {
    // eslint-disable-next-line no-undef
    chrome.runtime.onMessage.addListener((message) => {
      if (message.type === 'SETTINGS_UPDATED') {
        console.log('MURAi: Settings updated, reloading...');
        this.loadSettings().then(() => {
          this.clearAllHighlights();
          this.startDetection();
        });
      }
    });
  }

  startDetection() {
    if (!this.settings.protectionEnabled) {
      console.log('MURAi: Protection disabled');
      return;
    }

    console.log('MURAi: Starting detection...');
    
    // Clear any existing highlights first
    this.clearAllHighlights();
    
    // Run detection at different times to catch dynamic content
    this.detectAndHighlight();
    
    // Run when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.detectAndHighlight();
      });
    }

    // Run after page is fully loaded
    window.addEventListener('load', () => {
      this.detectAndHighlight();
    });

    // Run after a delay to catch any dynamic content
    setTimeout(() => {
      this.detectAndHighlight();
    }, 1000);

    // Set up observer for dynamic content
    this.setupMutationObserver();
  }

  setupMutationObserver() {
    const observer = new MutationObserver((mutations) => {
      let shouldRecheck = false;
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            shouldRecheck = true;
          }
        });
      });
      
      if (shouldRecheck) {
        setTimeout(() => this.detectAndHighlight(), 100);
      }
    });

    if (document.body) {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    } else {
      document.addEventListener('DOMContentLoaded', () => {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      });
    }
  }

  detectAndHighlight() {
    if (!this.settings.protectionEnabled || this.isProcessing) return;

    this.isProcessing = true;

    try {
      // Get current page text
      const pageText = document.body.innerText || document.body.textContent || '';
      console.log('MURAi: Page text length:', pageText.length);
      console.log('MURAi: Page text sample:', pageText.substring(0, 200));

      // Get terms to check based on language setting
      const termsToCheck = this.getTermsToCheck();
      console.log('MURAi: Terms to check:', termsToCheck);
      console.log('MURAi: Language setting:', this.settings.language);

      // Find terms in page text
      const foundTerms = this.findTermsInText(pageText, termsToCheck);
      console.log('MURAi: Search completed, found terms:', foundTerms);
      
      if (foundTerms.length === 0) {
        console.log('MURAi: No inappropriate terms found');
        this.isProcessing = false;
        return;
      }

      console.log('MURAi: Found terms:', foundTerms);
      this.detectedTerms = foundTerms;

      // Report detected words to database
      console.log('MURAi: About to report', foundTerms.length, 'detected words to database');
      foundTerms.forEach(term => {
        console.log('MURAi: Reporting term:', term);
        this.reportDetectedWord(term, pageText, 'Profanity');
      });

      // Highlight found terms
      this.highlightTerms(foundTerms);
    } catch (error) {
      console.error('MURAi: Error in detection:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  clearAllHighlights() {
    // Remove all existing MURAi highlights
    const existingHighlights = document.querySelectorAll('.murai-highlight');
    existingHighlights.forEach(highlight => {
      // Remove the murai-highlight class
      highlight.classList.remove('murai-highlight');
      
      // Reset the styles
      highlight.style.removeProperty('background-color');
      highlight.style.removeProperty('filter');
      highlight.style.removeProperty('padding');
      highlight.style.removeProperty('border-radius');
      highlight.style.removeProperty('border');
      
      // If it was an asterisk replacement, restore original content
      if (highlight.innerHTML && highlight.innerHTML.includes('*'.repeat(3))) {
        // This was an asterisk replacement, we need to restore original text
        // For now, just remove the highlighting
        console.log('MURAi: Restoring asterisk content');
      }
    });
    
    // Clear processed elements tracking
    this.processedElements = new WeakSet();
  }

  getTermsToCheck() {
    // Get base terms for the selected language
    let terms = [...(TERMS_BY_LANGUAGE[this.settings.language] || TERMS_BY_LANGUAGE['Mixed'])];
    console.log('MURAi: Base terms for language', this.settings.language, ':', terms);

    // Filter out whitelisted terms
    terms = terms.filter(term =>
      !this.settings.whitelistTerms.some(whitelisted =>
        term.toLowerCase().includes(whitelisted.toLowerCase())
      )
    );
    console.log('MURAi: After whitelist filtering:', terms);

    // Adjust based on sensitivity
    if (this.settings.sensitivity === 'Low') {
      // Only the most severe terms
      terms = terms.filter(term =>
        ['fuck', 'shit', 'putangina', 'tangina', 'gago', 'gagi', 'gaga', 'puta', 'kinuha', 'kinuhamo'].includes(term.toLowerCase())
      );
    } else if (this.settings.sensitivity === 'Medium') {
      // Filter out milder terms
      terms = terms.filter(term => 
        !['stupid', 'idiot', 'moron', 'dumb', 'fool', 'bobo', 'tanga'].includes(term.toLowerCase())
      );
    }
    // High sensitivity keeps all terms

    console.log(`MURAi: After sensitivity filtering (${this.settings.sensitivity}):`, terms);
    console.log(`MURAi: Using ${terms.length} terms for ${this.settings.language} language with ${this.settings.sensitivity} sensitivity`);
    return terms;
  }

  // Helper to build a robust regex for a term
  buildTermRegex(term) {
    // Allow for start/end, whitespace, or punctuation around the term
    // Handles multi-word terms and punctuation
    // Example: (?:^|\s|[.,!?;:()"'])term(?:$|\s|[.,!?;:()"'])
    const pattern = `(?:^|\\s|[.,!?;:()"'])${this.escapeRegex(term)}(?:$|\\s|[.,!?;:()"'])`;
    return new RegExp(pattern, 'gi');
  }

  findTermsInText(text, terms) {
    const foundTerms = [];
    const lowerText = text.toLowerCase();

    console.log('MURAi: Searching in text (first 100 chars):', lowerText.substring(0, 100));
    console.log('MURAi: Checking', terms.length, 'terms');

    terms.forEach(term => {
      const regex = this.buildTermRegex(term.toLowerCase());
      const isFound = regex.test(lowerText);

      if (isFound) {
        console.log(`MURAi: ✅ FOUND term "${term}" using regex:`, regex);
        foundTerms.push(term);
      } else {
        // Only log for specific terms we're testing
        if (term.includes('gagi') || term.includes('gago') || term.includes('kinuha')) {
          console.log(`MURAi: ❌ NOT found term "${term}" using regex:`, regex);
        }
      }
    });

    console.log('MURAi: Final found terms:', foundTerms);
    return foundTerms;
  }

  highlightTerms(terms) {
    // Only skip processing if showHighlight is off AND we're not using blur style
    if (!this.settings.showHighlight && this.settings.flagStyle !== 'blur') return;

    // Focus on text content elements, not large containers
    const textElements = document.querySelectorAll('p, span, h1, h2, h3, h4, h5, h6, li, td, th, .userContent, ._5pbx, ._1dwg, ._1w_m');
    
    console.log('MURAi: Processing', textElements.length, 'text elements');
    
    textElements.forEach(element => {
      this.processElement(element, terms);
    });
  }

  processElement(element, terms) {
    // Skip if already processed or contains highlights
    if (this.processedElements.has(element) || element.classList.contains('murai-highlight')) {
      return;
    }
    if (element.closest('.murai-highlight')) {
      return;
    }
    const elementSize = element.offsetWidth * element.offsetHeight;
    if (elementSize > 50000) {
      return;
    }
    if (element.children.length > 5) {
      return;
    }
    const text = element.textContent || element.innerText || '';
    if (text.length > 500) {
      return;
    }
    if (text.trim().length < 3) {
      return;
    }
    const hasInappropriateContent = terms.some(term => {
      const regex = this.buildTermRegex(term);
      return regex.test(text);
    });
    if (!hasInappropriateContent) {
      return;
    }
    // --- New logic: wrap detected words in span with masking and actions ---
    let newContent = text;
    terms.forEach(term => {
      const regex = this.buildTermRegex(term);
      newContent = newContent.replace(regex, (match) => {
        const clean = match.trim();
        if (!clean) return match;
        const masked = '*'.repeat(clean.length);
        // Actions menu beside the word
        const actionsMenu = `
          <span class="murai-actions-menu" style="display:none; position:absolute; left:100%; top:50%; transform:translateY(-50%); margin-left:8px; background:#fff; border:1px solid #ccc; border-radius:4px; box-shadow:0 2px 8px rgba(0,0,0,0.15); z-index:10000; padding:2px 4px; white-space:nowrap;">
            <button class="murai-unmask-btn" style="font-size:10px; margin-right:4px;">Unmask</button>
            <button class="murai-view-btn" style="font-size:10px; margin-right:4px;">View</button>
            <button class="murai-report-btn" style="font-size:10px;">Report</button>
          </span>`;
        return match.replace(
          clean,
          `<span class="murai-masked-word" data-original="${clean}" style="position:relative;">${masked}${actionsMenu}</span>`
        );
      });
    });
    element.innerHTML = newContent;
    element.style.setProperty('background-color', this.settings.highlightColor, 'important');
    element.style.setProperty('padding', '4px', 'important');
    element.style.setProperty('border-radius', '3px', 'important');
    element.classList.add('murai-highlight');
    this.processedElements.add(element);
    if (!window.muraiHoverInjected) {
      this.injectHoverUnmaskScript();
      window.muraiHoverInjected = true;
    }
  }

  injectHoverUnmaskScript() {
    // CSS for actions menu and modern tooltip
    const style = document.createElement('style');
    style.innerHTML = `
      .murai-masked-word { position: relative; cursor: pointer; }
      .murai-highlight:hover .murai-masked-word .murai-actions-menu { display: inline-block !important; }
      .murai-actions-menu button { background: #444; color: #fff; border: none; border-radius: 3px; cursor: pointer; padding: 2px 6px; margin: 0 2px; transition: background 0.2s; }
      .murai-actions-menu button:hover { background: #222; }
      /* Add styles for unmasked words */
      .murai-unmasked-word { position: relative; }
      .murai-unmasked-word .murai-mask-btn { 
        display: none;
        background: #444;
        color: #fff;
        border: none;
        border-radius: 4px;
        padding: 2px 10px;
        cursor: pointer;
        font-size: 12px;
        margin-left: 8px;
        transition: background 0.2s;
      }
      /* Show mask button on hover */
      .murai-unmasked-word:hover .murai-mask-btn { 
        display: inline-block !important; 
      }
      .murai-unmasked-word .murai-mask-btn:hover {
        background: #222;
      }
      .murai-modern-tooltip {
        position: absolute;
        left: 50%;
        top: -38px;
        transform: translateX(-50%);
        background: #18181b;
        color: #fff;
        padding: 10px 18px;
        border-radius: 8px;
        font-size: 15px;
        white-space: nowrap;
        z-index: 10001;
        pointer-events: none;
        opacity: 0.97;
        box-shadow: 0 6px 32px rgba(0,0,0,0.13);
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Segoe UI', 'Arial', sans-serif;
        font-weight: 500;
        animation: murai-fadein 0.18s;
        border: 1px solid #232323;
      }
      .murai-modern-tooltip-arrow {
        position: absolute;
        left: 50%;
        top: 100%;
        transform: translateX(-50%);
        width: 18px;
        height: 8px;
        overflow: visible;
        pointer-events: none;
      }
      .murai-modern-tooltip-arrow svg {
        display: block;
      }
      @keyframes murai-fadein {
        from { opacity: 0; transform: translateX(-50%) translateY(10px); }
        to { opacity: 0.97; transform: translateX(-50%) translateY(0); }
      }
    `;
    document.head.appendChild(style);
    // Add CSS for Mask button hover on phrase
    style.innerHTML += `
      .murai-mask-btn { display: none; }
      .murai-highlight:hover .murai-unmasked-word .murai-mask-btn { display: inline-block !important; }
    `;
    // Event delegation for actions
    document.body.addEventListener('click', function(e) {
      // Unmask
      if (e.target.classList.contains('murai-unmask-btn')) {
        const span = e.target.closest('.murai-masked-word');
        if (!span) return;
        const isMasked = !span.classList.contains('murai-unmasked');
        if (isMasked) {
          // Confirmation modal logic
          const doUnmask = () => {
            // Remove highlight from parent phrase
            const parent = span.closest('.murai-highlight');
            if (parent) {
              parent.classList.remove('murai-highlight');
              parent.style.removeProperty('background-color');
              parent.style.removeProperty('padding');
              parent.style.removeProperty('border-radius');
            }
            
            // Replace span with plain text and Mask button
            const original = span.getAttribute('data-original');
            const wrapper = document.createElement('span');
            wrapper.className = 'murai-unmasked-word';
            wrapper.textContent = original;
            
            // Add Mask button
            const maskBtn = document.createElement('button');
            maskBtn.textContent = 'Mask';
            maskBtn.className = 'murai-mask-btn';
            
            // Attach click handler for re-masking
            maskBtn.onclick = function(ev) {
              ev.stopPropagation();
              const original = wrapper.textContent.replace('Mask', '').trim();
              const masked = '*'.repeat(original.length);
              const actionsMenu = `
                <span class="murai-actions-menu" style="display:none; position:absolute; left:100%; top:50%; transform:translateY(-50%); margin-left:8px; background:#fff; border:1px solid #ccc; border-radius:4px; box-shadow:0 2px 8px rgba(0,0,0,0.15); z-index:10000; padding:2px 4px; white-space:nowrap;">
                  <button class="murai-unmask-btn" style="font-size:10px; margin-right:4px;">Unmask</button>
                  <button class="murai-view-btn" style="font-size:10px; margin-right:4px;">View</button>
                  <button class="murai-report-btn" style="font-size:10px;">Report</button>
                </span>`;
              const maskedSpan = document.createElement('span');
              maskedSpan.className = 'murai-masked-word';
              maskedSpan.setAttribute('data-original', original);
              maskedSpan.style.position = 'relative';
              maskedSpan.innerHTML = masked + actionsMenu;
              
              // Restore highlight to parent
              const highlightParent = wrapper.parentElement;
              if (highlightParent) {
                highlightParent.classList.add('murai-highlight');
                highlightParent.style.backgroundColor = '#ffeb3b';
                highlightParent.style.padding = '4px';
                highlightParent.style.borderRadius = '3px';
              }
              
              wrapper.replaceWith(maskedSpan);
            };
            
            wrapper.appendChild(maskBtn);
            span.replaceWith(wrapper);
          };
          if (!localStorage.getItem('muraiDontShowUnmaskConfirm')) {
            injectMuraiModal(doUnmask);
          } else {
            doUnmask();
          }
        }
        e.stopPropagation();
      }
      // Mask (delegated)
      if (e.target.classList.contains('murai-mask-btn')) {
        const wrapper = e.target.closest('.murai-unmasked-word');
        if (!wrapper) return;
        const original = wrapper.textContent.replace('Mask', '').trim();
        const masked = '*'.repeat(original.length);
        const actionsMenu = `
          <span class="murai-actions-menu" style="display:none; position:absolute; left:100%; top:50%; transform:translateY(-50%); margin-left:8px; background:#fff; border:1px solid #ccc; border-radius:4px; box-shadow:0 2px 8px rgba(0,0,0,0.15); z-index:10000; padding:2px 4px; white-space:nowrap;">
            <button class="murai-unmask-btn" style="font-size:10px; margin-right:4px;">Unmask</button>
            <button class="murai-view-btn" style="font-size:10px; margin-right:4px;">View</button>
            <button class="murai-report-btn" style="font-size:10px;">Report</button>
          </span>`;
        const maskedSpan = document.createElement('span');
        maskedSpan.className = 'murai-masked-word';
        maskedSpan.setAttribute('data-original', original);
        maskedSpan.style.position = 'relative';
        maskedSpan.innerHTML = masked + actionsMenu;
        // Restore highlight to parent
        const highlightParent = wrapper.parentElement;
        if (highlightParent) {
          highlightParent.classList.add('murai-highlight');
          highlightParent.style.backgroundColor = '#ffeb3b';
          highlightParent.style.padding = '4px';
          highlightParent.style.borderRadius = '3px';
        }
        wrapper.replaceWith(maskedSpan);
        e.stopPropagation();
      }
      // View
      if (e.target.classList.contains('murai-view-btn')) {
        alert('View action for: ' + e.target.closest('.murai-masked-word').getAttribute('data-original'));
        e.stopPropagation();
      }
      // Report
      if (e.target.classList.contains('murai-report-btn')) {
        const word = e.target.closest('.murai-masked-word')?.getAttribute('data-original') || '';
        injectMuraiReportModal(word);
        e.stopPropagation();
      }
    });
    // Modern tooltip logic for highlighted phrase
    document.body.addEventListener('mouseenter', function(e) {
      if (e.target.classList.contains('murai-highlight')) {
        // Prevent duplicate tooltips
        if (e.target.querySelector('.murai-modern-tooltip')) return;
        // Tooltip container
        const tooltip = document.createElement('div');
        tooltip.className = 'murai-modern-tooltip';
        tooltip.innerHTML = `
          <span>Sensitive content detected. Actions available.</span>
          <span class="murai-modern-tooltip-arrow">
            <svg width="18" height="8"><polygon points="0,0 9,8 18,0" style="fill:#18181b;" /></svg>
          </span>
        `;
        // Position tooltip absolutely relative to the .murai-highlight
        e.target.style.position = 'relative';
        e.target.appendChild(tooltip);
      }
    }, true);
    document.body.addEventListener('mouseleave', function(e) {
      if (e.target.classList.contains('murai-highlight')) {
        const tooltip = e.target.querySelector('.murai-modern-tooltip');
        if (tooltip) tooltip.remove();
      }
    }, true);
  }

  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // Report detected word to database
  async reportDetectedWord(word, context, patternType = 'Profanity') {
    try {
      console.log('MURAi: Starting report for word:', word);

      // Check if already reported to avoid duplicates
      const reportKey = `${word}-${window.location.href}`;
      if (this.reportedWords.has(reportKey)) {
        console.log('MURAi: Word already reported, skipping:', reportKey);
        return;
      }

      // Get auth data from localStorage
      const authDataStr = localStorage.getItem('murai_auth_data');
      console.log('MURAi: Auth data found:', !!authDataStr);

      if (!authDataStr) {
        console.log('MURAi: No auth data found, skipping report');
        return;
      }

      const authData = JSON.parse(authDataStr);
      console.log('MURAi: Auth data parsed, token exists:', !!authData.token);

      if (!authData.token) {
        console.log('MURAi: No auth token found, skipping report');
        return;
      }

      // Determine pattern type based on word
      let detectedPatternType = 'Profanity';
      const word_lower = word.toLowerCase();

      if (['gago', 'putangina', 'tangina', 'fuck', 'shit', 'bitch'].includes(word_lower)) {
        detectedPatternType = 'Severe Profanity';
      } else if (['stupid', 'idiot', 'bobo', 'tanga'].includes(word_lower)) {
        detectedPatternType = 'Mild Profanity';
      } else if (['hate', 'kill', 'die'].includes(word_lower)) {
        detectedPatternType = 'Hate Speech';
      }

      // Determine severity based on sensitivity and word
      let severity = 'medium';
      if (['putangina', 'fuck', 'motherfucker', 'cunt'].includes(word_lower)) {
        severity = 'high';
      } else if (['stupid', 'idiot', 'bobo'].includes(word_lower)) {
        severity = 'low';
      }

      // Determine site type
      let siteType = 'Website';
      const hostname = window.location.hostname.toLowerCase();
      if (hostname.includes('facebook') || hostname.includes('twitter') || hostname.includes('instagram')) {
        siteType = 'Social Media';
      } else if (hostname.includes('reddit') || hostname.includes('forum')) {
        siteType = 'Forum';
      } else if (hostname.includes('youtube')) {
        siteType = 'Video Platform';
      }

      const reportData = {
        word: word.trim(),
        context: context.substring(0, 200), // Limit context length
        url: window.location.href,
        patternType: detectedPatternType,
        language: this.settings.language,
        severity: severity,
        siteType: siteType
      };

      console.log('MURAi: Reporting detected word:', reportData);
      console.log('MURAi: API URL:', `${this.API_BASE_URL}/users/detected-words`);

      const response = await fetch(`${this.API_BASE_URL}/users/detected-words`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authData.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportData)
      });

      console.log('MURAi: Response status:', response.status);
      console.log('MURAi: Response ok:', response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('MURAi: Response data:', result);
        this.reportedWords.add(reportKey);
        console.log('MURAi: Successfully reported detected word');
      } else {
        const errorText = await response.text();
        console.error('MURAi: Failed to report detected word:', response.status, errorText);
      }

    } catch (error) {
      console.error('MURAi: Error reporting detected word:', error);
    }
  }

  getFlagSymbol() {
    console.log('MURAi: Current flag style:', this.settings.flagStyle);
    
    switch (this.settings.flagStyle) {
      case 'asterisk': 
        console.log('MURAi: Using asterisk flag');
        return '*';
      case 'blur': 
        console.log('MURAi: Using blur style (no flag)');
        return '';
      case 'highlight': 
        console.log('MURAi: Using highlight style (no flag)');
        return '';
      default: 
        console.log('MURAi: Using default asterisk flag');
        return '*';
    }
  }
}

// Modal HTML injection helper
function injectMuraiModal(onConfirm) {
  if (document.getElementById('murai-confirm-modal')) return;
  const modal = document.createElement('div');
  modal.id = 'murai-confirm-modal';
  modal.innerHTML = `
    <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(30,30,30,0.45);z-index:100000;display:flex;align-items:center;justify-content:center;">
      <div style="background:#fff;border:1px solid #e5e7eb;padding:28px 22px;border-radius:12px;box-shadow:0 6px 32px rgba(0,0,0,0.13);min-width:320px;max-width:90vw;">
        <div style="font-size:18px;font-weight:600;margin-bottom:10px;color:#18181b;">Unmask sensitive word?</div>
        <div style="font-size:15px;margin-bottom:16px;color:#52525b;">Are you sure you want to reveal this word? This may expose sensitive content.</div>
        <label style="display:flex;align-items:center;font-size:14px;margin-bottom:16px;cursor:pointer;color:#71717a;">
          <input type="checkbox" id="murai-dont-show-again" style="margin-right:8px;accent-color:#18181b;" /> Don't show this confirmation again
        </label>
        <div style="display:flex;gap:10px;justify-content:flex-end;">
          <button id="murai-cancel-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;font-size:15px;cursor:pointer;transition:background 0.15s;">Cancel</button>
          <button id="murai-confirm-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #18181b;background:#18181b;color:#fff;font-size:15px;cursor:pointer;transition:background 0.15s;">Unmask</button>
        </div>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
  document.getElementById('murai-cancel-btn').onclick = () => {
    modal.remove();
  };
  document.getElementById('murai-confirm-btn').onclick = () => {
    const dontShow = document.getElementById('murai-dont-show-again').checked;
    if (dontShow) localStorage.setItem('muraiDontShowUnmaskConfirm', '1');
    modal.remove();
    onConfirm();
  };
}

// Add the report modal helper at the end of the file:
function injectMuraiReportModal(word) {
  if (document.getElementById('murai-report-modal')) return;
  const modal = document.createElement('div');
  modal.id = 'murai-report-modal';
  modal.innerHTML = `
    <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(30,30,30,0.45);z-index:100000;display:flex;align-items:center;justify-content:center;">
      <div style="background:#fff;border:1px solid #e5e7eb;padding:28px 22px;border-radius:12px;box-shadow:0 6px 32px rgba(0,0,0,0.13);min-width:340px;max-width:95vw;">
        <div style="font-size:18px;font-weight:600;margin-bottom:10px;color:#18181b;">Report flagged word</div>
        <div style="font-size:15px;margin-bottom:16px;color:#52525b;">What is this report about?</div>
        <select id="murai-report-reason" style="width:100%;padding:8px 6px;margin-bottom:12px;font-size:15px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;">
          <option value="false-positive">False positive (not offensive)</option>
          <option value="offensive">Offensive content</option>
          <option value="other">Other</option>
        </select>
        <textarea id="murai-report-comment" placeholder="Add a comment (optional)" style="width:100%;min-height:60px;resize:vertical;padding:8px 6px;font-size:15px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;margin-bottom:16px;"></textarea>
        <div style="display:flex;gap:10px;justify-content:flex-end;">
          <button id="murai-report-cancel-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;font-size:15px;cursor:pointer;transition:background 0.15s;">Cancel</button>
          <button id="murai-report-send-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #18181b;background:#18181b;color:#fff;font-size:15px;cursor:pointer;transition:background 0.15s;">Send</button>
        </div>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
  document.getElementById('murai-report-cancel-btn').onclick = () => {
    modal.remove();
  };
  document.getElementById('murai-report-send-btn').onclick = async () => {
    const reason = document.getElementById('murai-report-reason').value;
    const comment = document.getElementById('murai-report-comment').value;

    console.log('MURAi: Manual report - Word:', word, 'Reason:', reason, 'Comment:', comment);

    // Send report to database
    try {
      // Get content script instance to call reportDetectedWord
      if (window.muraiContentScript) {
        await window.muraiContentScript.reportDetectedWord(
          word,
          `Manual report: ${reason}. Comment: ${comment}`,
          'Manual Report'
        );
      } else {
        console.error('MURAi: Content script instance not found');
      }
    } catch (error) {
      console.error('MURAi: Error sending manual report:', error);
    }

    // Show success message in a modal overlay
    const box = modal.querySelector('div > div');
    box.innerHTML = `
      <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(30,30,30,0.45);z-index:100000;display:flex;align-items:center;justify-content:center;">
        <div style="background:#fff;border:1px solid #e5e7eb;padding:28px 22px;border-radius:12px;box-shadow:0 6px 32px rgba(0,0,0,0.13);min-width:320px;max-width:90vw;display:flex;flex-direction:column;align-items:center;">
          <div style="font-size:18px;font-weight:600;margin-bottom:10px;color:#18181b;">Report sent</div>
          <div style="font-size:15px;margin-bottom:16px;color:#52525b;">Thank you for your feedback!</div>
          <button id="murai-report-close-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #18181b;background:#18181b;color:#fff;font-size:15px;cursor:pointer;transition:background 0.15s;">Close</button>
        </div>
      </div>
    `;
    document.getElementById('murai-report-close-btn').onclick = () => {
      modal.remove();
    };
  };
}

// --- Custom context menu for reporting selected text ---
document.addEventListener('contextmenu', function(e) {
  // Only show if there is a text selection and not inside a murai highlight
  const selection = window.getSelection();
  const selectedText = selection && selection.toString().trim();
  if (selectedText && !e.target.closest('.murai-highlight')) {
    // Remove any existing custom menu
    const oldMenu = document.getElementById('murai-context-menu');
    if (oldMenu) oldMenu.remove();
    // Create custom menu
    const menu = document.createElement('div');
    menu.id = 'murai-context-menu';
    menu.textContent = 'Report inappropriate text';
    menu.style.position = 'fixed';
    menu.style.left = e.clientX + 'px';
    menu.style.top = e.clientY + 'px';
    menu.style.background = '#fff';
    menu.style.border = '1px solid #e5e7eb';
    menu.style.borderRadius = '8px';
    menu.style.boxShadow = '0 4px 24px rgba(0,0,0,0.13)';
    menu.style.padding = '10px 18px';
    menu.style.fontSize = '15px';
    menu.style.color = '#18181b';
    menu.style.cursor = 'pointer';
    menu.style.zIndex = 100001;
    menu.onmousedown = (ev) => { ev.preventDefault(); };
    menu.onclick = (ev) => {
      ev.preventDefault();
      menu.remove();
      injectMuraiReportModalForText(selectedText);
    };
    document.body.appendChild(menu);
    // Remove menu on click elsewhere or scroll
    setTimeout(() => {
      document.addEventListener('mousedown', removeMenu, { once: true });
      document.addEventListener('scroll', removeMenu, { once: true });
    }, 0);
    function removeMenu() {
      if (menu) menu.remove();
    }
  }
}, true);

function injectMuraiReportModalForText(text) {
  if (document.getElementById('murai-report-modal')) return;
  const modal = document.createElement('div');
  modal.id = 'murai-report-modal';
  modal.innerHTML = `
    <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(30,30,30,0.45);z-index:100000;display:flex;align-items:center;justify-content:center;">
      <div style="background:#fff;border:1px solid #e5e7eb;padding:28px 22px;border-radius:12px;box-shadow:0 6px 32px rgba(0,0,0,0.13);min-width:340px;max-width:95vw;">
        <div style="font-size:18px;font-weight:600;margin-bottom:10px;color:#18181b;">Report selected text</div>
        <div style="font-size:15px;margin-bottom:10px;color:#52525b;">Selected:</div>
        <div style="font-size:15px;margin-bottom:16px;color:#18181b;background:#f4f4f5;border-radius:6px;padding:8px 10px;word-break:break-word;">${text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</div>
        <div style="font-size:15px;margin-bottom:16px;color:#52525b;">What is this report about?</div>
        <select id="murai-report-reason" style="width:100%;padding:8px 6px;margin-bottom:12px;font-size:15px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;">
          <option value="false-positive">False positive (not offensive)</option>
          <option value="offensive">Offensive content</option>
          <option value="other">Other</option>
        </select>
        <textarea id="murai-report-comment" placeholder="Add a comment (optional)" style="width:100%;min-height:60px;resize:vertical;padding:8px 6px;font-size:15px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;margin-bottom:16px;"></textarea>
        <div style="display:flex;gap:10px;justify-content:flex-end;">
          <button id="murai-report-cancel-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #e5e7eb;background:#f4f4f5;color:#18181b;font-size:15px;cursor:pointer;transition:background 0.15s;">Cancel</button>
          <button id="murai-report-send-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #18181b;background:#18181b;color:#fff;font-size:15px;cursor:pointer;transition:background 0.15s;">Send</button>
        </div>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
  document.getElementById('murai-report-cancel-btn').onclick = () => {
    modal.remove();
  };
  document.getElementById('murai-report-send-btn').onclick = async () => {
    const reason = document.getElementById('murai-report-reason').value;
    const comment = document.getElementById('murai-report-comment').value;

    console.log('MURAi: Manual report - Text:', text, 'Reason:', reason, 'Comment:', comment);

    // Send report to database
    try {
      // Get content script instance to call reportDetectedWord
      if (window.muraiContentScript) {
        await window.muraiContentScript.reportDetectedWord(
          text,
          `Manual report: ${reason}. Comment: ${comment}`,
          'Manual Report'
        );
      } else {
        console.error('MURAi: Content script instance not found');
      }
    } catch (error) {
      console.error('MURAi: Error sending manual report:', error);
    }

    // Show success message in a modal overlay
    const box = modal.querySelector('div > div');
    box.innerHTML = `
      <div style="position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(30,30,30,0.45);z-index:100000;display:flex;align-items:center;justify-content:center;">
        <div style="background:#fff;border:1px solid #e5e7eb;padding:28px 22px;border-radius:12px;box-shadow:0 6px 32px rgba(0,0,0,0.13);min-width:320px;max-width:90vw;display:flex;flex-direction:column;align-items:center;">
          <div style="font-size:18px;font-weight:600;margin-bottom:10px;color:#18181b;">Report sent</div>
          <div style="font-size:15px;margin-bottom:16px;color:#52525b;">Thank you for your feedback!</div>
          <button id="murai-report-close-btn" style="padding:6px 18px;border-radius:6px;border:1px solid #18181b;background:#18181b;color:#fff;font-size:15px;cursor:pointer;transition:background 0.15s;">Close</button>
        </div>
      </div>
    `;
    document.getElementById('murai-report-close-btn').onclick = () => {
      modal.remove();
    };
  };
}

// --- Floating report button for selected text ---
document.addEventListener('mouseup', function(e) {
  setTimeout(() => { // Wait for selection to update
    const selection = window.getSelection();
    const selectedText = selection && selection.toString().trim();
    // Only show if there is a text selection and not inside a murai highlight
    if (selectedText && !e.target.closest('.murai-highlight')) {
      // Remove any existing report button
      const oldBtn = document.getElementById('murai-report-btn-floating');
      if (oldBtn) oldBtn.remove();
      // Get selection position
      let rect;
      try {
        rect = selection.getRangeAt(0).getBoundingClientRect();
      } catch { rect = null; }
      if (!rect || (rect.left === 0 && rect.top === 0)) return;
      // Create floating button
      const btn = document.createElement('button');
      btn.id = 'murai-report-btn-floating';
      btn.textContent = 'Report';
      btn.style.position = 'fixed';
      btn.style.left = (rect.right + 8) + 'px';
      btn.style.top = (rect.top - 8) + 'px';
      btn.style.background = '#fff';
      btn.style.border = '1px solid #e5e7eb';
      btn.style.borderRadius = '8px';
      btn.style.boxShadow = '0 4px 24px rgba(0,0,0,0.13)';
      btn.style.padding = '6px 16px';
      btn.style.fontSize = '15px';
      btn.style.color = '#18181b';
      btn.style.cursor = 'pointer';
      btn.style.zIndex = 100001;
      btn.style.transition = 'background 0.15s';
      btn.style.pointerEvents = 'all';
      btn.onmousedown = (ev) => { ev.preventDefault(); };
      btn.onclick = (ev) => {
        ev.preventDefault();
        btn.remove();
        injectMuraiReportModalForText(selectedText);
        window.getSelection().removeAllRanges();
      };
      document.body.appendChild(btn);
      // Remove button on click elsewhere, scroll, or selection change
      setTimeout(() => {
        document.addEventListener('mousedown', function handler(ev) {
          if (ev.target !== btn) removeBtn();
        }, { once: true });
        document.addEventListener('scroll', removeBtn, { once: true });
        document.addEventListener('selectionchange', removeBtn, { once: true });
      }, 0);
      function removeBtn() {
        if (btn) btn.remove();
      }
    } else {
      // Remove any existing report button if selection is empty
      const oldBtn = document.getElementById('murai-report-btn-floating');
      if (oldBtn) oldBtn.remove();
    }
  }, 0);
}, true);

// Initialize content script
console.log('MURAi: Initializing ContentScript...');
const contentScript = new ContentScript();
window.muraiContentScript = contentScript; // Make it globally accessible
contentScript.init();
console.log('MURAi: ContentScript initialized successfully');
