import React from "react";
import Logo from '../../assets/Logo.svg';

const Login = () => {
  const handleLogin = () => {
    if (window.chrome && chrome.runtime && chrome.runtime.getURL) {
      const url = chrome.runtime.getURL('login.html');
      window.open(url, '_blank');
    } else {
      window.open('/login.html', '_blank');
    }
  };

  return (
    <main
      style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', padding: 32, minWidth: 320 }}
      role="main"
      aria-label="Login section"
    >
      <img
        src={Logo}
        alt="Murai Extension Logo"
        style={{ width: 80, marginBottom: 24 }}
      />
      <h2 style={{ margin: 0, fontWeight: 700 }} id="login-header">
        Please login
      </h2>
      <p style={{ margin: '8px 0 24px 0', color: '#666', fontSize: 15 }} id="login-desc">
        Sign in to access your settings and features.
      </p>
      <button
        onClick={handleLogin}
        style={{ padding: '10px 32px', fontSize: 16, borderRadius: 6, background: '#2d72d9', color: '#fff', border: 'none', fontWeight: 600, cursor: 'pointer', boxShadow: '0 2px 8px rgba(0,0,0,0.07)' }}
        aria-label="Login to Murai Extension"
        aria-describedby="login-header login-desc"
        autoFocus
      >
        Login
      </button>
    </main>
  );
};

export default Login; 