/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

/* App Container */
.app-container {
  width: 380px;
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  border: 1px solid rgba(2, 185, 127, 0.1);
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 0 5px;
}

.logo {
  height: 32px;
}

/* Account Section */
.account-section {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.account-section:hover {
  opacity: 0.8;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: #555;
}

.account-avatar {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 9px;
  font-weight: bold;
}

.account-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.account-name {
  font-weight: 500;
  color: #333;
  font-size: 11px;
  line-height: 1.2;
}

.account-status {
  font-size: 9px;
  color: #02B97F;
  line-height: 1.2;
}

/* Section Titles */
.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
}

/* Language Selector */
.language-selector {
  padding: 15px;
  background-color: #fdfdfd;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.button-group {
  display: flex;
  gap: 0;
}

.language-btn {
  flex: 1;
  padding: 8px 4px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  font-size: 12px;
  font-weight: 500;
}

.language-btn:not(:last-child) {
  border-right: none;
}

.language-btn:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.language-btn:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.language-btn.active {
  background-color: #333;
  color: white;
  border-color: #333;
}

/* Sensitivity Selector */
.sensitivity-selector {
  padding: 15px;
  background-color: #fdfdfd;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.sensitivity-btn {
  flex: 1;
  padding: 8px 4px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  font-size: 12px;
  font-weight: 500;
}

.sensitivity-btn:not(:last-child) {
  border-right: none;
}

.sensitivity-btn:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.sensitivity-btn:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.sensitivity-btn.active {
  background-color: #333;
  color: white;
  border-color: #333;
}

/* Toggle Buttons */
.toggle-btn {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
  cursor: pointer;
  margin-bottom: 10px;
}

.protection-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fdfdfd;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.left-section {
  flex: 1;
}

.protection-label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.right-section {
  display: flex;
  align-items: center;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + .slider {
  background-color: #02B97F;
}

.toggle-switch input:checked + .slider:before {
  -webkit-transform: translateX(20px);
  -ms-transform: translateX(20px);
  transform: translateX(20px);
}

.option-btn {
  width: 100%;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
  cursor: pointer;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.option-btn:hover {
  background-color: #e0e0e0;
  border-color: #999;
}

.options-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Whitelist Section */
.whitelist-section {
  padding: 15px;
  background-color: #fdfdfd;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-top: 10px;
}

.whitelist-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.whitelist-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Poppins', sans-serif;
}

.whitelist-input:focus {
  outline: none;
  border-color: #02B97F;
}

.add-btn {
  padding: 8px 12px;
  background-color: #02B97F;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-btn:hover {
  background-color: #029f6b;
}

.whitelist-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 12px;
}

.whitelist-item-text {
  flex: 1;
  margin-right: 8px;
}

.whitelist-item-actions {
  display: flex;
  gap: 4px;
}

.edit-btn, .remove-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.edit-btn {
  background-color: #374151;
  color: white;
}

.edit-btn:hover {
  background-color: #1f2937;
}

.remove-btn {
  background-color: #f44336;
  color: white;
}

.remove-btn:hover {
  background-color: #d32f2f;
}

/* UI Customization Section */
.ui-section {
  padding: 15px;
  background-color: #fdfdfd;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-top: 10px;
}

.ui-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.ui-option-label {
  font-size: 12px;
  color: #555;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ui-option-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker {
  width: 24px;
  height: 24px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.color-picker:focus {
  outline: none;
  border-color: #02B97F;
}

/* Status */
.status {
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
}

.status-text {
  margin: 0;
  font-size: 16px;
}

.settings-text {
  margin: 5px 0 0;
  font-size: 13px;
  color: #666;
}

.setting-value {
  font-weight: 500;
}

.status.off {
  color: red;
}

.status.on {
  color: #02B97F;
}

.toggle-btn:hover {
  border-color: #667eea;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.protection-btn:hover {
  border-color: #667eea;
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.footer-info .setting-value {
  font-weight: normal;
}

.footer-info .status {
  font-weight: bold;
}

.footer a {
  color: #007bff;
}

.footer a:hover {
  text-decoration: underline;
}

/* Animation for smooth transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-container {
  animation: fadeIn 0.3s ease-out;
}

/* Focus states for accessibility */
button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Custom scrollbar for any overflow content */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* UI Customization Button Selections */
.ui-btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.ui-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 11px;
  border-radius: 4px;
  font-weight: 500;
}

.ui-btn:hover {
  background-color: #f5f5f5;
  border-color: #bbb;
}

.ui-btn.active {
  background-color: #333;
  color: white;
  border-color: #333;
}

.color-btn {
  width: 24px;
  height: 24px;
  border: 2px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.color-btn:hover {
  border-color: #999;
  transform: scale(1.1);
}

.color-btn.active {
  border-color: #333;
  transform: scale(1.1);
}

.color-btn.active::after {
  content: '✓';
  position: absolute;
  top: -2px;
  right: -2px;
  background: #333;
  color: white;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
}

/* Sample Text Preview */
.sample-text-section {
  margin-bottom: 15px;
  padding: 12px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}

.sample-text-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.sample-text {
  font-size: 13px;
  line-height: 1.4;
  color: #333;
}

.sample-text.asterisk .flagged {
  color: #e74c3c;
  font-weight: bold;
}

.sample-text.asterisk .flagged::before {
  content: '';
}

.sample-text.asterisk .flagged-word {
  color: #e74c3c;
  font-weight: bold;
}

.sample-text.asterisk .flagged-word::before {
  content: '';
}

.sample-text.asterisk .flagged-word .letter {
  color: transparent;
  font-weight: bold;
}

.sample-text.asterisk .flagged-word .letter::before {
  content: '*';
  color: #e74c3c;
  font-weight: bold;
}

.sample-text.asterisk.highlight .flagged-word .letter::before {
  content: '*';
  color: #e74c3c;
  font-weight: bold;
  background-color: var(--highlight-color, #ffeb3b);
  padding: 1px 2px;
  border-radius: 2px;
}

.sample-text.blur .flagged {
  filter: blur(2px);
  color: #999;
}

.sample-text.highlight .flagged {
  background-color: var(--highlight-color, #ffeb3b);
  padding: 1px 2px;
  border-radius: 2px;
}

/* Footer */
.footer {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
}

.footer-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-link {
  color: #007bff;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.footer-text {
  font-size: 11px;
  color: #666;
  margin: 0;
}

/* Save Button */
.save-btn {
  width: 100%;
  padding: 12px;
  background-color: #02B97F;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.save-btn:hover {
  background-color: #029f6b;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.save-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Modal */
.modal {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  max-width: 300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  text-align: center;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.modal-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.4;
}

.modal-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.modal-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-btn.confirm {
  background-color: #02B97F;
  color: white;
}

.modal-btn.confirm:hover {
  background-color: #029f6b;
}

.modal-btn.cancel {
  background-color: #6c757d;
  color: white;
}

.modal-btn.cancel:hover {
  background-color: #5a6268;
}

