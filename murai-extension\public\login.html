<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Login</title>
  <style>
    :root {
      --container-width: 420px;
      --primary-color: #02B97F;
      --primary-hover: #029f6b;
      --text-primary: #374151;
      --text-secondary: #6b7280;
      --background: #f8fafc;
      --surface: #ffffff;
      --border-radius: 16px;
    }

    body {
      font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
      background: var(--background);
      margin: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      -webkit-font-smoothing: antialiased;
    }

    .container {
      background: linear-gradient(135deg, var(--surface) 0%, #f8fafc 100%);
      border-radius: var(--border-radius);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
      padding: 48px 40px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: var(--container-width);
      max-width: 90vw;
      border: 1px solid rgba(2, 185, 127, 0.1);
    }
    .logo {
      width: 100px;
      margin-bottom: 32px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    h2 {
      margin: 0;
      font-weight: 600;
      font-size: 1.75rem;
      color: var(--text-primary);
      letter-spacing: -0.025em;
    }

    p {
      margin: 12px 0 32px 0;
      color: var(--text-secondary);
      font-size: 1rem;
      text-align: center;
      line-height: 1.5;
    }

    form {
      display: flex;
      flex-direction: column;
      width: 100%;
      gap: 20px;
      margin-bottom: 24px;
    }

    input {
      padding: 16px 20px;
      font-size: 1rem;
      border-radius: 12px;
      border: 1px solid #e5e7eb;
      background: #f9fafb;
      transition: all 0.2s ease;
      font-family: inherit;
    }

    input:focus {
      outline: none;
      border-color: var(--primary-color);
      background: var(--surface);
      box-shadow: 0 0 0 3px rgba(2, 185, 127, 0.1);
    }

    button {
      padding: 16px 32px;
      font-size: 1.1rem;
      border-radius: 12px;
      background: var(--primary-color);
      color: var(--surface);
      border: none;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(2, 185, 127, 0.3);
      transition: all 0.2s ease;
      font-family: inherit;
    }

    button:hover {
      background: var(--primary-hover);
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(2, 185, 127, 0.4);
    }

    button:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .success {
      color: var(--primary-color);
      font-weight: 600;
      margin-bottom: 20px;
      padding: 12px 20px;
      background: rgba(2, 185, 127, 0.1);
      border-radius: 8px;
      text-align: center;
    }

    .sync-info {
      background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
      border: 1px solid rgba(2, 185, 127, 0.2);
      border-radius: 12px;
      padding: 20px;
      margin-top: 24px;
      text-align: left;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(2, 185, 127, 0.1);
    }

    .sync-info h3 {
      margin: 0 0 12px 0;
      color: var(--primary-color);
      font-size: 16px;
      font-weight: 600;
    }

    .sync-info p {
      margin: 0;
      color: var(--text-primary);
      line-height: 1.5;
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="assets/Logo.svg" alt="Logo" class="logo" />
    <h2>Login Page</h2>
    <p>Sign in to your account to continue.</p>
    <form id="login-form" autocomplete="off">
      <input type="email" id="username" placeholder="Email" aria-label="Email" required />
      <input type="password" id="password" placeholder="Password" aria-label="Password" required />
      <button type="submit">Login</button>
    </form>

    <div class="sync-info">
      <h3>🔄 Mobile App Integration</h3>
      <p>Login with your mobile app credentials to sync your settings automatically. Your preferences, whitelist, and customizations will be synchronized between the extension and mobile app.</p>
    </div>

    <div id="success-message" class="success" style="display:none;">
      ✅ Login successful! Settings synced from mobile app.
    </div>
    <button id="open-popup-btn" style="display:none;">Open Murai Settings</button>
  </div>
  <script src="login.js"></script>
</body>
</html> 