{"name": "murai-mobile-server", "version": "1.0.0", "description": "Backend server for Murai Mobile app with group activity tracking", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "seed": "node scripts/seedData.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["express", "mongodb", "group-management", "activity-tracking", "mobile-app"], "author": "Your Name", "license": "MIT"}